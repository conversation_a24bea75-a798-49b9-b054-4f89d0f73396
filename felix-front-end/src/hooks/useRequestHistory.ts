import { useState, useEffect, useCallback } from 'react';
import requestService, { Request, RequestsResponse, GetRequestsParams } from '../api/services/requestService';

export interface RequestHistoryFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string;
}

export interface UseRequestHistoryReturn {
  requests: Request[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    hasMore: boolean;
    firstId: string | null;
    lastId: string | null;
  };
  filters: RequestHistoryFilters;
  setFilters: (filters: RequestHistoryFilters) => void;
  loadMore: () => Promise<void>;
  loadPrevious: () => Promise<void>;
  refresh: () => Promise<void>;
  initialize: () => Promise<void>;
}

export function useRequestHistory(initialLimit: number = 20): UseRequestHistoryReturn {
  const [requests, setRequests] = useState<Request[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    hasMore: false,
    firstId: null as string | null,
    lastId: null as string | null,
  });
  const [filters, setFilters] = useState<RequestHistoryFilters>({});

  const fetchRequests = useCallback(async (params: GetRequestsParams = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      // Check if requestService is available
      if (!requestService || typeof requestService.getRequests !== 'function') {
        throw new Error('Request service is not available');
      }

      const response: RequestsResponse = await requestService.getRequests({
        limit: initialLimit,
        order: 'desc', // Most recent first
        ...params,
      });

      // Validate response structure
      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response from server');
      }

      return {
        object: response.object || 'list',
        data: Array.isArray(response.data) ? response.data : [],
        first_id: response.first_id || null,
        last_id: response.last_id || null,
        has_more: Boolean(response.has_more),
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch requests';
      console.error('Error in fetchRequests:', err);
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [initialLimit]);

  const loadInitial = useCallback(async () => {
    try {
      const response = await fetchRequests();
      setRequests(response.data || []);
      setPagination({
        hasMore: response.has_more || false,
        firstId: response.first_id || null,
        lastId: response.last_id || null,
      });
    } catch (err) {
      console.error('Error loading initial requests:', err);
      setRequests([]);
      setPagination({
        hasMore: false,
        firstId: null,
        lastId: null,
      });
    }
  }, []);

  const loadMore = useCallback(async () => {
    if (!pagination.hasMore || isLoading) return;

    try {
      const response = await fetchRequests({
        after: pagination.lastId,
      });

      setRequests(prev => [...prev, ...(response.data || [])]);
      setPagination(prev => ({
        hasMore: response.has_more || false,
        firstId: prev.firstId, // Keep the original first ID
        lastId: response.last_id || null,
      }));
    } catch (err) {
      console.error('Error loading more requests:', err);
    }
  }, [fetchRequests, pagination.hasMore, pagination.lastId, isLoading]);

  const loadPrevious = useCallback(async () => {
    if (!pagination.firstId || isLoading) return;

    try {
      const response = await fetchRequests({
        before: pagination.firstId,
      });

      setRequests(prev => [...(response.data || []), ...prev]);
      setPagination(prev => ({
        hasMore: prev.hasMore,
        firstId: response.first_id || null,
        lastId: prev.lastId, // Keep the original last ID
      }));
    } catch (err) {
      console.error('Error loading previous requests:', err);
    }
  }, [fetchRequests, pagination.firstId, isLoading]);

  const refresh = useCallback(async () => {
    await loadInitial();
  }, []);

  // Don't load data automatically on mount to prevent errors
  // Instead, provide a manual initialization function
  const initialize = useCallback(async () => {
    try {
      await loadInitial();
    } catch (error) {
      console.error('Failed to load initial data:', error);
      // Set empty state on error
      setRequests([]);
      setError('Failed to load request history');
      setIsLoading(false);
    }
  }, []);

  // Filter requests based on current filters
  const filteredRequests = requests.filter(request => {
    // Safety check for request object
    if (!request || !request.id) {
      return false;
    }

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesId = request.id?.toLowerCase().includes(searchLower) || false;
      const matchesUserId = request.user_id?.toLowerCase().includes(searchLower) || false;
      if (!matchesId && !matchesUserId) {
        return false;
      }
    }

    // Date filters
    if (filters.dateFrom && request.created_at) {
      try {
        const requestDate = new Date(request.created_at);
        const fromDate = new Date(filters.dateFrom);
        if (isNaN(requestDate.getTime()) || isNaN(fromDate.getTime()) || requestDate < fromDate) {
          return false;
        }
      } catch (e) {
        return false;
      }
    }

    if (filters.dateTo && request.created_at) {
      try {
        const requestDate = new Date(request.created_at);
        const toDate = new Date(filters.dateTo);
        // Set to end of day for inclusive comparison
        toDate.setHours(23, 59, 59, 999);
        if (isNaN(requestDate.getTime()) || isNaN(toDate.getTime()) || requestDate > toDate) {
          return false;
        }
      } catch (e) {
        return false;
      }
    }

    // Status filter
    if (filters.status && filters.status !== 'all') {
      if (request.status !== filters.status) {
        return false;
      }
    }

    return true;
  });

  return {
    requests: filteredRequests,
    isLoading,
    error,
    pagination,
    filters,
    setFilters,
    loadMore,
    loadPrevious,
    refresh,
    initialize,
  };
}
